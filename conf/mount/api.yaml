zbcore_dat:
  service: zbcore_dat
  domain: http://dat-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcore_dau:
  service: zbcore_dau
  domain: http://dau.sell-course-base-dd.suanshubang.cc
  timeout: 1500ms
  httpStat: true
  retry: 1

accelerate:
  service: accelerate
  domain: http://accelerate.sell-trade-base-dd.suanshubang.cc
  timeout: 1500ms
  retry: 1

moat:
  service: moat
  domain: http://c3-sell-gateway-base-cc.suanshubang.cc
  timeout: 3000ms
  retry: 1

zbcore_dal:
  service: zbcore_dal
  domain: http://dal.sell-course-base-dd.suanshubang.cc
  timeout: 1500ms
  httpStat: true
  retry: 1

userprofile:
  # 调用下游的服务名称
  service: userprofile
  # 请求完整地址
  domain: http://assistantdesk-base-cc.suanshubang.cc
  # 超时配置，time.Duration 类型
  timeout: 10000ms
  # 重试次数，最多执行retry+1次
  retry: 2

mesh:
  service: mesh
  domain: http://assistantdesk-base-cc.suanshubang.cc
  appkey: 68DA5D3D7F2859B0
  timeout: 5000ms
  retry: 1

coursesearch:
  service: coursesearch
  domain: http://coursesearch-svc.sell-course:8080
  appkey: fwyy_allocate
  appsecret: 72695e2fc8ed87ffbd4fc5e02e9ea0ff
  timeout: 5000ms
  retry: 1

tower:
  service: tower
  domain: http://tower-svc.support:8080
  timeout: 1500ms
  retry: 1

su:
  service: su
  domain: http://sell-base-e.suanshubang.cc
  timeout: 1500ms
  retry: 1

passport:
  service: passport
#  domain: http://session-svc.saas:8080
  domain: http://passport-base-z.suanshubang.cc
  timeout: 1500ms
  retry: 1

assistantdesk:
  service: assistantdesk
  domain: http://assistantdesk-svc.support:8080
  timeout: 1500ms
  retry: 1

assistantai:
  service: assistantdesk
  domain: http://assistant-ai-svc.support-bailing:8080
  timeout: 1500ms
  retry: 1

#发号器
galaxy:
  # 调用下游的服务名称
  service: galaxy
  # 请求完整地址
  domain: http://galaxy.inf-base-dd.suanshubang.cc
  # 超时配置，time.Duration 类型
  timeout: 300ms
  # 重试次数，最多执行retry+1次
  retry: 1

kunpeng:
  service: kunpeng
  domain: http://kp.zuoyebang.cc
  timeout: 1500ms
  retry: 1

kpStaff:
  service: kunpeng
  domain: http://kp.zuoyebang.cc
  timeout: 1500ms
  retry: 1

# 水星平台
mercury:
  service: mercury
  domain: http://mercury-svc.edu:8080
  timeout: 3000ms
  retry: 3

plum:
  service: plum
  domain: http://plum-svc.support:8080
  timeout: 3000ms
  retry: 3

muse:
  service: muse
  domain: http://assistantdesk-xqfk-cc.suanshubang.cc
  timeout: 3000ms
  retry: 3

touchmis:
  service: touchmis
  domain: http://touchmis-svc.lpc:8080
  timeout: 3000ms
  retry: 3

officeServer:
  service: officeServer
  domain: ai-proxy.zuoyebang.cc
  timeout: 1500ms
  retry: 1
aiturbo:
  service: aiturbo
  domain: http://aiassistant-ycteng-cc.suanshubang.cc
  timeout: 1500ms
  retry: 1

asr2:
  service: asr2
  domain: http://172-29-228-12-8968.ip.tx-bj6-prod-online.dmz.zuoyebang.dd:80
  timeout: 5000ms
  retry: 3

lpcmsg:
  service: lpcmsg
  domain: http://lpcmsg-svc.lpc-lamy:8080
  timeout: 3000ms
  retry: 3

info_msg:
  service: info_msg
  domain: http://infomsg-base-cc.suanshubang.cc
  timeout: 1500ms
  retry: 1

classme:
  service: classme
  domain: http://classme-svc.edu-ljtz:8080
  timeout: 1500ms
  retry: 1
fwyy-evaluate:
  service: fwyy-evaluate
  domain: http://fwyy-evaluate-svc.support:8080
  timeout: 1500ms
  retry: 1
assistantcoursego:
  service: accelerate
  domain: http://assistantdesk-zxk-cc.suanshubang.cc
  timeout: 1500ms
  retry: 1
exercise:
  service: exercise
  domain: http://jx-notebook-e.suanshubang.cc
  timeout: 1500ms
  retry: 1
dataproxy:
  service: dataproxy
  domain: http://assistantdesk-xqfk-cc.suanshubang.cc
  timeout: 1500ms
  retry: 1

duxuesc:
  service: duxuesc
  domain: http://duxuesc-svc.lpc:8080
  timeout: 3000ms
  retry: 1

allocate:
  service: allocate
  domain: http://allocate-svc.lpc:8080
  timeout: 1000ms
  httpStat: true
  retry: 1

examcore:
  service: examcore
  domain: http://examcore-svc.edu:8080
  timeout: 3000ms
  retry: 1

coursebase:
  service: coursebase
  domain: https://sellmis.zuoyebang.cc
  timeout: 3000ms
  retry: 1

touchmisgo:
  service: touchmisgo
  domain: http://assistantdesk-xqfk-cc.suanshubang.cc
  timeout: 3000ms
  retry: 1

ucloud:
  service: ucloud
  domain: http://ucloud.saas-base-dd.suanshubang.cc
  timeout: 2000ms
  retry: 2

longlink:
  # 调用下游的服务名称
  service: longlink
  # 请求完整地址
  domain: http://10.116.252.48:8092
  # 超时配置，time.Duration 类型
  timeout: 1000ms
  # 重试次数，最多执行retry+1次
  retry: 2

zbtikuapi:
  service: zbtikuapi
  domain: http://zbtikuapi-svc.tiku:8080
  timeout: 3000ms
  retry: 1
  appkey: 11000039
  appsecret: 0b0f41c57f3fe7496983218d21f8dfe9

coursetransgo:
  service: coursetransgo
  domain: http://assistantdesk-base-cc.suanshubang.cc
  timeout: 2000ms
  retry: 2

agg:
  service: agg
  domain: http://agg-svc.support:8080
  timeout: 2000ms
  retry: 2

eduprobe:
  service: eduprobe
  domain: http://eduprobe-svc.support:8080
  timeout: 2000ms
  retry: 2

laxinmis:
  service: kunpeng
  domain: http://dianxiao-base-cc.suanshubang.cc
  timeout: 1500ms
  retry: 1

miscourse:
  service: miscourse
  domain: https://sellmis-base-cc.suanshubang.cc
  timeout: 1500ms
  retry: 1

jxreport:
  service: jxreport
  domain: http://jxreport.edu-thebigread-dd.suanshubang.cc
  timeout: 2000ms
  retry: 1

jwbiz:
  service: jwbiz
  domain: http://jwbiz-svc.sell-course-thebigread:8080
  timeout: 2000ms
  retry: 1