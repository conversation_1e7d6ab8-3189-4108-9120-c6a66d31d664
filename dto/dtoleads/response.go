package dtoleads

// Feature 模型特征结构
type Feature struct {
	Name   string `json:"name" mapstructure:"name"`     // 特征名称
	Desc   string `json:"desc" mapstructure:"desc"`     // 特征描述
	Value  string `json:"value" mapstructure:"value"`   // 特征值
	Weight int    `json:"weight" mapstructure:"weight"` // 特征权重
}

type GetLeadsLayerDetailInfoRsp struct {
	Features           []Feature           `json:"features"`
	PurchaseIntentions []PurchaseIntention `json:"purchaseIntentions"`
	Milestones         []Milestone         `json:"milestones"`
	RefreshTime        string              `json:"refreshTime"`
	TransLevel         string              `json:"transLevel"`
}

type PurchaseIntention struct {
	Date  string  `json:"date"`
	Score float64 `json:"score"`
}

type Milestone struct {
	Date string `json:"date"`
	Desc string `json:"desc"`
}
